<?php

AppModel::loadModelClass('IndufastWorkdaySpecialHoursModel');

class IndufastWorkdaySpecialHours extends IndufastWorkdaySpecialHoursModel {
  
  use ModelFillTrait;
  use ModelTimeTrait;
  use ValidationTrait;
  use PropertyCastTrait;

  const string TYPE_LEAVE = 'leave';
  const string TYPE_SPECIAL_LEAVE = 'special-leave';
  const string TYPE_SICK = 'sick';

  protected array $fillable = [
    'workday_id' => 'required|integer|exists:indufast_workday,id',
    'duration'   => 'required|date:H:i:s',
    'type'       => 'required|in:leave,special-leave,sick',
  ];

  const array CAST_PROPERTIES = [
    'id'         => 'int',
    'workday_id' => 'int',
    'from_db'    => 'hidden',
  ];
}