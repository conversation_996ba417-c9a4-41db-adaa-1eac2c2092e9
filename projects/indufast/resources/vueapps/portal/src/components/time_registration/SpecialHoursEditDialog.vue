<script setup>
import {ref, watch, computed} from "vue";
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";
import { vMaska } from "maska/vue"

const router = useRouter();
const api = createApiService(router);

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  specialHours: {
    type: Object,
    default: null
  },
  workday: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

const loading = ref(false);
const formData = ref({
  type: '',
  duration: '',
});

const specialHoursTypes = [
  {value: 'leave', title: 'Verlof'},
  {value: 'special-leave', title: 'Bijzonder verlof'},
  {value: 'sick', title: 'Ziek'},
];

const isEditing = computed(() => props.specialHours && props.specialHours.id);

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.specialHours) {
    formData.value = {
      type: props.specialHours.type || '',
      duration: formatDurationForDisplay(props.specialHours.duration || ''),
    };
  } else if (newValue) {
    formData.value = {
      type: '',
      duration: '',
    };
  }
});

const formatDurationForDisplay = (duration) => {
  if (!duration) return '';
  // Convert HH:MM:SS to HH:MM for display
  return duration.substring(0, 5);
};

const formatDurationForBackend = (duration) => {
  if (!duration) return '';
  // Convert HH:MM to HH:MM:SS for backend
  return duration.includes(':') && duration.split(':').length === 2 ? `${duration}:00` : duration;
};

const closeDialog = () => {
  emit('update:modelValue', false);
};

const saveSpecialHours = async () => {
  loading.value = true;
  
  try {
    const data = {
      type: formData.value.type,
      duration: formatDurationForBackend(formData.value.duration),
      workday_id: props.workday.id,
    };

    const endpoint = isEditing.value 
      ? `workdaySpecialHoursUpdate?id=${props.specialHours.id}`
      : 'workdaySpecialHoursCreate';

    const response = await api.post(endpoint, JSON.stringify(data));
    
    emit('saved', response.data.data);
    closeDialog();
  } catch (error) {
    console.error('Error saving special hours:', error);
  } finally {
    loading.value = false;
  }
};

const isFormValid = computed(() => {
  return formData.value.type && formData.value.duration;
});
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="500"
  >
    <v-card>
      <v-card-title class="bg-primary">
        {{ isEditing ? 'Bijzondere uren bewerken' : 'Bijzondere uren toevoegen' }} op {{ workday.date }}
      </v-card-title>
      <v-card-text>
        <v-select
          v-model="formData.type"
          :items="specialHoursTypes"
          label="Type"
          prepend-icon="mdi-format-list-bulleted-type"
          required
        />
        <v-text-field
          v-model="formData.duration"
          v-maska="'##:##'"
          placeholder="08:00"
          label="Duur"
          prepend-icon="mdi-clock-time-four-outline"
          required
        />
      </v-card-text>
      <v-card-actions>
        <v-btn
          text="Annuleren"
          @click="closeDialog"
        />
        <v-btn
          text="Opslaan"
          color="primary"
          variant="elevated"
          :disabled="!isFormValid"
          :loading="loading"
          @click="saveSpecialHours"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
